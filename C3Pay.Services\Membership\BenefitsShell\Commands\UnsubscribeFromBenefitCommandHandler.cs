﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.Membership.Benefit;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.Services.Membership.BenefitsShell.Commands
{
    public class UnsubscribeFromBenefitCommandHandler : IRequestHandler<UnsubscribeFromBenefitCommand, Result<BenefitBaseDto<BenefitUnsubscribeDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBenefitService _benefitService;
        private readonly IMediator _mediator;
        private readonly ILogger _logger;
        private readonly ITextMessageSenderService _textMessageSenderService;

        public UnsubscribeFromBenefitCommandHandler(
            IUnitOfWork unitOfWork,
            IBenefitService benefitService,
            IMediator mediator,
            ILogger<UnsubscribeFromBenefitCommandHandler> logger,
            ITextMessageSenderService textMessageSenderService)
        {
            _unitOfWork = unitOfWork;
            _benefitService = benefitService;
            _mediator = mediator;
            _logger = logger;
            _textMessageSenderService = textMessageSenderService;
        }

        public async Task<Result<BenefitBaseDto<BenefitUnsubscribeDto>>> Handle(UnsubscribeFromBenefitCommand command, CancellationToken ct)
        {
            try
            {
                // Validate command
                var isCommandValid = IsCommandValid(command);
                if (isCommandValid.IsFailure)
                {
                    _logger.LogError(isCommandValid.Error.Code);
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(isCommandValid.Error);
                }

                // Get benefit by ID
                var benefit = await _unitOfWork.Benefits.FirstOrDefaultAsync(x => x.Id == command.BenefitId);
                if (benefit == null)
                {
                    _logger.LogError(Errors.VpnMembershipErrors.MembershipNotAvailable.Code);
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.MembershipNotAvailable);
                }

                // Get user for eligibility and subscription checks
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.PhoneNumber == command.UserPhoneNumber && !u.IsDeleted);
                if (user == null)
                {
                    _logger.LogError(Errors.VpnMembershipErrors.UserNotFound.Code);
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.UserNotFound);
                }

                // Check if user has active subscription using BenefitService
                var subscriptionResult = await _benefitService.HasActiveBenefitSubscription(command.UserPhoneNumber, benefit.Id, ct);
                if (subscriptionResult.IsFailure)
                {
                    _logger.LogError(subscriptionResult.Error.Code);
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(subscriptionResult.Error);
                }
                
                bool hasActiveSubscription = subscriptionResult.Value;

                if (!hasActiveSubscription)
                {
                    _logger.LogError(Errors.VpnMembershipErrors.MembershipNotActive.Code);
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.MembershipNotActive);
                }

                // Delegate to benefit-specific unsubscription logic
                if (benefit.Name.Equals(ConstantParam.VpnBenefitName, StringComparison.OrdinalIgnoreCase))
                {
                    return await HandleVpnUnsubscription(command, benefit, user.Id, ct);
                }
                else
                {
                    _logger.LogError($"Unsubscription not yet implemented for benefit: {benefit.Name}");
                    return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.MembershipNotAvailable);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unsubscribing from benefit {command.BenefitId}: {ex.Message}");
                return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.CantCreateSubscription);
            }
        }

        private async Task<Result<BenefitBaseDto<BenefitUnsubscribeDto>>> HandleVpnUnsubscription(UnsubscribeFromBenefitCommand command, Benefit benefit, Guid userId, CancellationToken ct)
        {
            // Find the active VPN membership for this user
            var vpnMembershipUser = await _unitOfWork.VpnMembershipUsers.FirstOrDefaultAsync(v => 
                v.UserId == userId && v.IsActive);
            
            if (vpnMembershipUser == null)
            {
                _logger.LogError(Errors.VpnMembershipErrors.MembershipNotActive.Code);
                return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.MembershipNotActive);
            }

            // Update the VPN membership to cancel it
            vpnMembershipUser.Cancel();
            var cancellationDate = vpnMembershipUser.UserCanceledOn.Value;

            // Save changes to the database
            try
            {
                await _unitOfWork.CommitAsync();
                _logger.LogInformation("VPN membership cancelled for user {UserPhoneNumber} at {CancellationDate}", 
                    command.UserPhoneNumber, cancellationDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save VPN membership cancellation for user {UserPhoneNumber}", command.UserPhoneNumber);
                return Result.Failure<BenefitBaseDto<BenefitUnsubscribeDto>>(Errors.VpnMembershipErrors.CantCreateSubscription);
            }
            
            // Create unsubscribe response
            var genericResponse = new BenefitUnsubscribeDto
            {
                BenefitName = benefit.Name,
                BenefitCode = benefit.CustomName ?? "VPN",
                UnsubscribeDate = cancellationDate,
                BenefitExpiryDate = vpnMembershipUser.ValidUntill.ToDayWithSuffix(),
                RefundAmount = 0, // No refund for VPN
                IsActive = false,
                Status = "Cancelled",
                ProviderSpecificData = new
                {
                    BenefitType = "VPN",
                    CancellationReason = "User requested cancellation",
                    VpnCode = vpnMembershipUser.Code
                }
            };

            return Result.Success(new BenefitBaseDto<BenefitUnsubscribeDto>
            {
                IsSuccessful = true,
                Data = genericResponse
            });
        }

        private static Result IsCommandValid(UnsubscribeFromBenefitCommand command)
        {
            if (command is null)
            {
                return Result.Failure(Errors.VpnMembershipErrors.EmptyRequest);
            }

            if (string.IsNullOrWhiteSpace(command.UserPhoneNumber))
            {
                return Result.Failure(Errors.VpnMembershipErrors.NoPhoneNumberSent);
            }

            if (command.BenefitId <= 0)
            {
                return Result.Failure(Errors.VpnMembershipErrors.MembershipNotAvailable);
            }

            return Result.Success();
        }
    }
}