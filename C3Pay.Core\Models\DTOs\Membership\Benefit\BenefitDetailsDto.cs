﻿using System;
using System.Collections.Generic;
using C3Pay.Core.Models.C3Pay.Membership.BenefitsShell;

namespace C3Pay.Core.Models.DTOs.Membership.Benefit
{
    // Generic base DTO for benefit responses
    public class BenefitBaseDto<T>
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public T Data { get; set; }
    }

    // Generic benefit details DTO (expandable for all benefit types)
    public class BenefitDetailsDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal Price { get; set; }
        public string ProductTitle { get; set; }
        public string ProductSubtitle { get; set; }
        public string TermsAndConditionsUrl { get; set; }
        public string PrimaryCtaTitle { get; set; }
        public string SecondaryCtaTitle { get; set; }
        public List<BenefitFeature> ProductDefinitions { get; set; } = [];
        public List<BenefitVideoDto> Videos { get; set; } = [];
        // Additional fields from Benefit entity
        public string Provider { get; set; }
        public string Deeplink { get; set; }
        public string Code { get; set; }
        // Subscription status
        public bool IsUserSubscribed { get; set; }
        // Dashboard icon metadata
        public int? DashboardIconId { get; set; }
        public string DashboardIconUrl { get; set; }
        public string DashboardIconTitle { get; set; }
        public int? DashboardIconOrder { get; set; }
        public string DashboardIconDeeplink { get; set; }
        public string DashboardIconSubtitle { get; set; }
        // Confirmation data for benefit subscription
        public BenefitConfirmationDto Confirmation { get; set; }
    }

    public class BenefitMainCardDto
    {
        public string Header { get; set; }
        public string Footer { get; set; }
        public BenefitCardCta CardCta { get; set; }
    }

    public class BenefitCardCta
    {
        public string Title { get; set; }
    }

    public class BenefitConfirmationDto
    {
        public string Title { get; set; }
        public string Subtitle { get; set; }
        public List<BenefitVideoDto> OnboardingVideoUrl { get; set; } = [];
        public string FinalCtaText { get; set; }
    }

    // Generic benefit subscription response DTO
    public class BenefitSubscribeDto
    {
        public string BenefitName { get; set; }
        public string BenefitCode { get; set; }
        public DateTime SubscriptionStartDate { get; set; }
        public DateTime SubscriptionEndDate { get; set; }
        public DateTime NextBillingDate { get; set; }
        public decimal AmountCharged { get; set; }
        public bool IsActive { get; set; }
        // Provider-specific data (VPN codes, insurance details, etc.)
        public object ProviderSpecificData { get; set; }
    }

    // Generic benefit unsubscribe response DTO
    public class BenefitUnsubscribeDto
    {
        public string BenefitName { get; set; }
        public string BenefitCode { get; set; }
        public DateTime UnsubscribeDate { get; set; }
        public decimal RefundAmount { get; set; }
        public bool IsActive { get; set; }
        public string Status { get; set; }
        // Provider-specific data (VPN cleanup info, insurance termination details, etc.)
        public object ProviderSpecificData { get; set; }
        public string BenefitExpiryDate { get; set; }
    }
}
