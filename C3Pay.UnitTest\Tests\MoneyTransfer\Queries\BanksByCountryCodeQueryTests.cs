using C3Pay.Core;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Services.MoneyTransfer;
using C3Pay.UnitTest.Tests.MoneyTransfer.Fixtures;
using FluentAssertions;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace C3Pay.UnitTest.Tests.MoneyTransfer.Queries
{
    public class BanksByCountryCodeQueryTests : IClassFixture<BanksByCountryCodeQueryTestsFixture>
    {
        private readonly BanksByCountryCodeQueryTestsFixture _fixture;
        private readonly BanksByCountryCodeQueryHandler _handler;
        public BanksByCountryCodeQueryTests(BanksByCountryCodeQueryTestsFixture fixture)
        {
            _fixture = fixture;
            _handler = new BanksByCountryCodeQueryHandler(_fixture.UnitOfWorkMock.Object,
                _fixture.CacheServiceMock.Object);

        }

        [Fact]
        public async Task Hanlde_CacheHitWithData_ReturnsDataFromCache()
        {
            _fixture.ResetMocks();
            // Arrange
            _fixture.SetupCacheToGenerateKey();
            _fixture.SetupCacheToReturnData();

            // Act
            var result = await _handler.Handle(_fixture.Request, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Equal(_fixture.CacheResult);
            _fixture.CacheServiceMock.Verify(c => c.GetAsync<IList<MoneyTransferBank>>(_fixture.Key), Times.Once);
            _fixture.UnitOfWorkMock.Verify(x => x.MoneyTransferBanks.FindAsync(It.IsAny<Expression<Func<MoneyTransferBank, bool>>>()), Times.Never);
        }
        [Fact]
        public async Task Handle_ShouldReturnSortedBanks_WhenCacheMissAndDatabaseHitWithData()
        {
            _fixture.ResetMocks();
            // Arrange
            _fixture.SetupCacheToGenerateKey();
            _fixture.SetupCacheToReturnNull();
            _fixture.SetupDatabaseToReturnUnSortedData();
            _fixture.SetupCacheToSetSortedData();

            // Act
            var result = await _handler.Handle(_fixture.Request, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            var sortedBanks = result.Value.ToList();

            sortedBanks.Should().BeEquivalentTo(_fixture.SortedData, options => options.WithStrictOrdering());

            _fixture.UnitOfWorkMock.Verify(x => x.MoneyTransferBanks.FindAsync(It.IsAny<Expression<Func<MoneyTransferBank, bool>>>()), Times.Once);
            _fixture.CacheServiceMock.Verify(c => c.SetAsync(_fixture.Key, _fixture.SortedData, It.IsAny<TimeSpan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_CacheMissDatabaseMiss_ReturnsEmptyResult()
        {
            _fixture.ResetMocks();
            // Arrange
            _fixture.SetupCacheToReturnNull();
            _fixture.SetupDatabaseToReturnEmptyList();

            // Act
            var result = await _handler.Handle(_fixture.Request, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Equal(_fixture.EmptyResponse);
        }

        [Fact]
        public void ApplyIndiaValidation_ShouldSetAccountNumberLength_ForUnvalidatedIndiaBanks()
        {
            // Arrange
            var banks = _fixture.UnvalidatedIndiaBanks;

            // Act
            _handler.ApplyValidationRules("IN", banks);

            // Assert
            banks.Should().AllSatisfy(bank =>
            {
                bank.AccountNumberLengthLimitType.Should().Be(AccountNumberLengthLimitType.Range);
                bank.AccountNumberLengthLimitValues.Should().Be("6,18");
            });
        }

        [Fact]
        public void ApplyPakistanValidation_ShouldSetAccountNumberLength_ForUnvalidatedPakistanBanks()
        {
            // Arrange
            var banks = _fixture.UnvalidatedPakistanBanks;

            // Act
            _handler.ApplyValidationRules("PK", banks);

            // Assert
            banks.Should().AllSatisfy(bank =>
            {
                bank.AccountNumberLengthLimitType.Should().Be(AccountNumberLengthLimitType.Range);
                bank.AccountNumberLengthLimitValues.Should().Be("6,16");
            });
        }

        [Fact]
        public void ApplyDefaultValidation_ShouldSetAccountNumberLengthType_ToUseDefault()
        {
            // Arrange
            var banks = _fixture.DefaultBanks;

            // Act
            _handler.ApplyValidationRules("US", banks);

            // Assert
            banks.Should().AllSatisfy(bank =>
            {
                bank.AccountNumberLengthLimitType.Should().Be(AccountNumberLengthLimitType.UseDefault);
            });
        }
    }
}
